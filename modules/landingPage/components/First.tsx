import Logo from '@/modules/landingPage/assets/landing/logo.png';
import { navLinks } from '@/modules/landingPage/constant/Constant';
import { handleLogout } from 'helper/handleLogout';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { useAppDispatch, useAppSelector } from 'store/hooks';
// import { HiBars3 } from "react-icons/hi2";

export const HeaderPage = () => {
  const dispatch = useAppDispatch();
  const router = useRouter();
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const token = useAppSelector(
    (state) => state.persistedReducer.auth.access_token
  );
  const providerName = useAppSelector(
    (state) => state?.persistedReducer?.provider?.provider
  );

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const closeSidebar = () => {
    setIsSidebarOpen(false);
  };

  return (
    <>
      <header
        className={`sticky top-0 z-50 ${
          isSidebarOpen ? 'bg-transparent' : 'bg-white'
        }`}
      >
        <div className="mt-4 h-[12vh] w-full ">
          <div className="mx-auto flex h-full w-[90%] items-center justify-between xl:w-[80%]">
            <div className="flex items-center gap-4">
              {/* Mobile & Tablet Hamburger Menu */}
              <button
                onClick={toggleSidebar}
                className="p-2 lg:hidden"
                aria-label="Toggle menu"
              >
                <Image src="/sidebar.png" alt="menu" width={24} height={24} />
              </button>

              <Link
                href="/"
                className="flex items-center justify-between gap-2"
              >
                <Image src={Logo} alt="logo image" width={39} height={39} />
                <span className="text-xl font-semibold text-primary">
                  FITSOMNIA
                </span>
              </Link>
            </div>

            <div className="hidden w-full items-center justify-between lg:flex lg:w-auto">
              <ul className="flex flex-col p-4 lg:flex-row lg:space-x-8 lg:border-0 lg:p-0">
                {navLinks.map((item) => {
                  return (
                    <li key={item.label}>
                      <Link href={item.link}>{item.label}</Link>
                    </li>
                  );
                })}
              </ul>
            </div>

            <div className="hidden items-center space-x-4 lg:flex">
              {!token ? (
                <p className="align-items inline-flex justify-center rounded-full bg-[#25D366] px-4 py-2 font-medium text-white">
                  <Link
                    prefetch={false}
                    data-testid="login-account-link"
                    href="/account/sign-in"
                  >
                    Log In
                  </Link>
                  /
                  <Link
                    prefetch={false}
                    data-testid="login-account-link"
                    href="/account/sign-up"
                  >
                    Registration
                  </Link>
                </p>
              ) : (
                <a
                  onClick={() =>
                    handleLogout(dispatch, providerName, router)
                  }
                  className="align-items inline-flex cursor-pointer justify-center rounded-full bg-[#25D366] px-4 py-2 font-medium text-white transition-all duration-100 ease-linear"
                >
                  logout
                </a>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Mobile & Tablet Sidebar Overlay */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={closeSidebar}
        />
      )}

      {/* Mobile & Tablet Sidebar */}
      <div
        className={`fixed top-0 left-0 z-50 h-full w-64 transform bg-white shadow-lg transition-transform duration-300 ease-in-out lg:hidden ${
          isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        <div className="p-4">
          {/* Close button */}
          <button
            onClick={closeSidebar}
            className="absolute top-4 right-4 text-gray-600 hover:text-gray-800"
          >
            ✕
          </button>

          {/* Logo */}
          <div className="mb-8 mt-4">
            <Link
              href="/"
              className="flex items-center gap-2"
              onClick={closeSidebar}
            >
              <Image src={Logo} alt="logo image" width={39} height={39} />
              <span className="text-xl font-semibold text-primary">
                FITSOMNIA
              </span>
            </Link>
          </div>

          {/* Navigation Links */}
          <nav>
            <ul className="space-y-4">
              {navLinks.map((item) => (
                <li key={item.label}>
                  <Link
                    href={item.link}
                    className="block rounded py-2 px-4 text-gray-700 hover:bg-gray-100 hover:text-primary"
                    onClick={closeSidebar}
                  >
                    {item.label}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>

          {/* Auth buttons in sidebar */}
          <div className="mt-8">
            {!token ? (
              <div className="space-y-2">
                <Link
                  href="/account/sign-in"
                  className="block w-full rounded-full bg-[#25D366] py-2 px-4 text-center text-white"
                  onClick={closeSidebar}
                >
                  Log In
                </Link>
                <Link
                  href="/account/sign-up"
                  className="block w-full rounded-full border border-[#25D366] py-2 px-4 text-center text-[#25D366]"
                  onClick={closeSidebar}
                >
                  Registration
                </Link>
              </div>
            ) : (
              <button
                onClick={() => {
                  handleLogout(dispatch, providerName, router);
                  closeSidebar();
                }}
                className="block w-full rounded-full bg-[#25D366] py-2 px-4 text-center text-white"
              >
                Logout
              </button>
            )}
          </div>
        </div>
      </div>
    </>
  );
};
