import { signOut } from "next-auth/react";
import { NextRouter } from "next/router";
import { toast } from "react-toastify";
import { clearAuthState } from "store/slices/authSlice";
import { resetCart } from "store/slices/cartSlice";
import { deleteCheckoutInfo } from "store/slices/checkoutSlice";
import { resetCompare } from "store/slices/compareSlice";
import { resetAddress } from "store/slices/customerAddressSlice";
import { resetWishilist } from "store/slices/productsSlice";
import { storeProvider } from "store/slices/providerSlice";
import { resetUserDetails } from "store/slices/userSlice";
import { persistor } from "store/store";

export const handleLogout = async (dispatch: any, providerName: string, router: NextRouter) => {
  try {
    // Clear NextAuth session first if using social auth
    if (providerName !== 'none') {
      await signOut({ redirect: false });
    }

    // Clear Redux state immediately (synchronously) to update UI
    dispatch(clearAuthState());
    dispatch(resetAddress());
    dispatch(resetUserDetails());
    dispatch(resetWishilist());
    dispatch(resetCart());
    dispatch(resetCompare());
    dispatch(deleteCheckoutInfo());
    dispatch(storeProvider('none'));

    // Clear localStorage immediately
    localStorage.removeItem('persist:root');
    localStorage.clear();

    // Clear persisted storage
    await persistor.purge();

    // Show success message
    if (!router.query.clear) {
      toast.error('Logged out successfully!', {
        containerId: 'bottom-right',
      });
    }

    // Small delay to ensure state is fully cleared before redirect
    setTimeout(() => {
      router.push('/account/sign-in?clear=true');
    }, 100);

  } catch (error) {
    console.error('Logout error:', error);
    // Clear Redux state even if there's an error
    dispatch(clearAuthState());
    dispatch(storeProvider('none'));
    localStorage.clear();
    // Still redirect to sign-in
    router.push('/account/sign-in?clear=true');
  }
};