import { signOut } from "next-auth/react";
import { NextRouter } from "next/router";
import { toast } from "react-toastify";
import { clearAuthState } from "store/slices/authSlice";
import { resetCart } from "store/slices/cartSlice";
import { deleteCheckoutInfo } from "store/slices/checkoutSlice";
import { resetCompare } from "store/slices/compareSlice";
import { resetAddress } from "store/slices/customerAddressSlice";
import { resetWishilist } from "store/slices/productsSlice";
import { storeProvider } from "store/slices/providerSlice";
import { resetUserDetails } from "store/slices/userSlice";
import { persistor } from "store/store";

export const handleLogout = async (dispatch: any, providerName: string, router: NextRouter) => {
  // Clear Redux state immediately (synchronously) to update UI
  dispatch(clearAuthState());
  dispatch(resetAddress());
  dispatch(resetUserDetails());
  dispatch(resetWishilist());
  dispatch(resetCart());
  dispatch(resetCompare());
  dispatch(deleteCheckoutInfo());
  dispatch(storeProvider('none'));

  // Clear localStorage immediately
  localStorage.removeItem('persist:root');
  localStorage.clear();

  try {
    // Clear NextAuth session if using social auth
    if (providerName !== 'none') {
      await signOut({ redirect: false });
    }

    // Clear persisted storage
    await persistor.purge();

    // Show success message
    if (!router.query.clear) {
      toast.error('Logged out successfully!', {
        containerId: 'bottom-right',
      });
    }

    // Redirect to sign-in page
    router.push('/account/sign-in?clear=true');
  } catch (error) {
    console.error('Logout error:', error);
    // Even if there's an error, still redirect to sign-in
    router.push('/account/sign-in?clear=true');
  }
};